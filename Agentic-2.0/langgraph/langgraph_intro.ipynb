# Import LangGraph and other necessary libraries
from langgraph.graph import Graph
from IPython.display import Image, display

print("✅ LangGraph imported successfully!")
print("✅ Environment setup complete!")
print("✅ Ready to learn LangGraph!")

# 🔧 FUNCTION 1: Text Processor
# What it does: Takes input text and adds a signature
# Why: This simulates a processing step in our workflow

def function1(input1):
    """
    First processing function in our workflow.
    
    Args:
        input1 (str): The input text to process
    
    Returns:
        str: Processed text with added signature
    """
    result = input1 + " from first function"
    print(f"🔄 Function 1 processed: '{input1}' → '{result}'")
    return result

# 🔧 FUNCTION 2: Text Enhancer
# What it does: Takes processed text and adds another signature
# Why: This simulates a second processing step

def function2(input2):
    """
    Second processing function in our workflow.
    
    Args:
        input2 (str): The text from the previous function
    
    Returns:
        str: Further processed text with additional signature
    """
    result = input2 + " savita from second function"
    print(f"🔄 Function 2 processed: '{input2}' → '{result}'")
    return result

# 🔧 FUNCTION 3: Placeholder for Future Use
# What it does: Currently nothing (placeholder)
# Why: Shows how easy it is to add more functions later

def function3(input3):
    """
    Placeholder function for future workflow expansion.
    
    Args:
        input3: Input for future processing
    
    Returns:
        None: Currently does nothing
    """
    # TODO: Add functionality here when needed
    pass

# 🧪 Test Function 1
# Input: "sunny"
# Expected Output: "sunny from first function"

test_result_1 = function1("sunny")
test_result_1

# 🧪 Test Function 2
# Input: "savita"
# Expected Output: "savita savita from second function"

test_result_2 = function2("savita")
test_result_2

# 📦 Import the Graph class from LangGraph (if not already imported)
# What this does: Gives us access to the Graph functionality
# Why we need it: Graph is the main class for building workflows

# This import is already done in Step 1, but we include it here for clarity
# from langgraph.graph import Graph
print("✅ LangGraph already imported in Step 1!")

# 🏭 Create an empty workflow (factory)
# What this does: Creates a container for our workflow
# Why: We need a place to add our functions and connections

workflow1 = Graph()
print("✅ Empty workflow created!")
print(f"📊 Workflow object: {workflow1}")

# 🔧 Add Node 1: First Processing Station
# What this does: Registers function1 as a processing station named "fun1"
# Why: We need to tell the workflow what functions are available

workflow1.add_node("fun1", function1)
print("✅ Added 'fun1' node with function1")

# 🔧 Add Node 2: Second Processing Station
# What this does: Registers function2 as a processing station named "fun2"
# Why: We need multiple stations for our assembly line

workflow1.add_node("fun2", function2)
print("✅ Added 'fun2' node with function2")

# 🔗 Add Edge: Connect the Processing Stations
# What this does: Creates a conveyor belt from fun1 to fun2
# Why: Tells the workflow the order of processing

workflow1.add_edge("fun1", "fun2")
print("✅ Connected fun1 → fun2")
print("🔄 Data will flow: Input → fun1 → fun2 → Output")

# 🚪 Set Entry Point: Where to Start
# What this does: Tells the workflow to start at fun1
# Why: The workflow needs to know where to begin processing

workflow1.set_entry_point("fun1")
print("✅ Set entry point: fun1")
print("🚪 Workflow will start at fun1")

# 🏁 Set Exit Point: Where to End
# What this does: Tells the workflow to end at fun2
# Why: The workflow needs to know where to stop and return results

workflow1.set_finish_point("fun2")
print("✅ Set exit point: fun2")
print("🏁 Workflow will end at fun2")

# ⚙️ Compile the Workflow: Turn Blueprint into Executable App
# What this does: Validates connections and prepares workflow for execution
# Why: We need to "turn on the factory" before we can process data

app = workflow1.compile()
print("✅ Workflow compiled successfully!")
print("🏭 Factory is now ready to process data!")

# 📊 Inspect Workflow Structure
# What this shows: Internal structure of our compiled workflow
# Notice: LangGraph automatically added __start__ and __end__ nodes

print("📊 Workflow Structure:")
print("✅ Nodes: fun1, fun2, __start__, __end__")
print("✅ Edges: __start__ → fun1 → fun2 → __end__")

app.get_graph()

from IPython.display import Image, display

display(Image(app.get_graph().draw_mermaid_png()))

app.invoke("hi this is sunny")

for output in app.stream("hi this is rohit"):
    for key,value in output.items():
        print(f"here is output from {key}")
        print("_______")
        print(value)
        print("\n")
    

 from langchain_google_genai import ChatGoogleGenerativeAI
model=ChatGoogleGenerativeAI(model='gemini-1.5-flash')

model.invoke("hi").content

def llm(input):
    from langchain_google_genai import ChatGoogleGenerativeAI
    model=ChatGoogleGenerativeAI(model='gemini-1.5-flash')
    output=model.invoke(input)
    return output.content

def token_counter(input):
    token=input.split()
    token_number=len(token)
    return f"total token number in the generated answer is {token_number}"

workflow2=Graph()

workflow2.add_node("My_LLM",llm)

workflow2.add_node("LLM_Output_Token_Counter",token_counter)

workflow2.add_edge("My_LLM","LLM_Output_Token_Counter")

workflow2.set_entry_point("My_LLM")

workflow2.set_finish_point("LLM_Output_Token_Counter")

app=workflow2.compile()

display(Image(app.get_graph().draw_mermaid_png()))

app.invoke("can you tell me about the india's capital?")

app.invoke("tell me about the tata enterpirse in very detail.")

for output in app.stream("tell me about the tata enterpirse in very detail."):
    for key,value in output.items():
        print(f"here is output from {key}")
        print("_______")
        print(value)
        print("\n")

